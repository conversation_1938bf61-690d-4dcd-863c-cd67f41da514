#!/usr/bin/env python3
"""
Test script to verify the HFC normalization fix works correctly.
This script tests the get_robust_hfc_normalization_value function.
"""

import numpy as np
import sys
import os

# Add the project root to the path so we can import our modules
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_hfc_normalization_function():
    """Test the robust HFC normalization function"""
    
    print("Testing HFC normalization function...")
    
    try:
        from utils.processing import get_robust_hfc_normalization_value
        print("✓ Successfully imported get_robust_hfc_normalization_value")
    except ImportError as e:
        print(f"✗ Failed to import function: {e}")
        return False
    
    # Test 1: With HFC data only (should calculate from data)
    print("\nTest 1: Calculate from HFC data")
    test_hfc_data = np.array([0.1, 0.5, 1.0, 2.0, 5.0, 10.0])
    
    try:
        value, source = get_robust_hfc_normalization_value(hfc_data=test_hfc_data)
        expected_p95 = np.percentile(test_hfc_data, 95.0)
        print(f"  Returned value: {value}")
        print(f"  Source: {source}")
        print(f"  Expected P95: {expected_p95}")
        
        if abs(value - expected_p95) < 1e-6:
            print("  ✓ Correctly calculated P95 from data")
        else:
            print("  ✗ P95 calculation mismatch")
            return False
            
    except Exception as e:
        print(f"  ✗ Error in test 1: {e}")
        return False
    
    # Test 2: With plot_settings containing hfc_p95
    print("\nTest 2: Use value from plot_settings")
    plot_settings = {'hfc_p95': 3.5}
    
    try:
        value, source = get_robust_hfc_normalization_value(
            hfc_data=test_hfc_data, 
            plot_settings=plot_settings
        )
        print(f"  Returned value: {value}")
        print(f"  Source: {source}")
        
        if value == 3.5 and 'plot_settings' in source:
            print("  ✓ Correctly used value from plot_settings")
        else:
            print("  ✗ Failed to use plot_settings value")
            return False
            
    except Exception as e:
        print(f"  ✗ Error in test 2: {e}")
        return False
    
    # Test 3: Fallback to max when no percentile can be calculated
    print("\nTest 3: Fallback to max value")
    small_hfc_data = np.array([0.1, 0.2])
    
    try:
        value, source = get_robust_hfc_normalization_value(hfc_data=small_hfc_data)
        expected_max = np.max(np.abs(small_hfc_data))
        print(f"  Returned value: {value}")
        print(f"  Source: {source}")
        print(f"  Expected max: {expected_max}")
        
        # Should either calculate P95 or use max - both are valid
        if value > 0:
            print("  ✓ Returned positive normalization value")
        else:
            print("  ✗ Returned non-positive value")
            return False
            
    except Exception as e:
        print(f"  ✗ Error in test 3: {e}")
        return False
    
    # Test 4: No data fallback
    print("\nTest 4: No data fallback")
    
    try:
        value, source = get_robust_hfc_normalization_value()
        print(f"  Returned value: {value}")
        print(f"  Source: {source}")
        
        if value == 1.0 and 'no_data_fallback' in source:
            print("  ✓ Correctly used no-data fallback")
        else:
            print("  ✗ Unexpected fallback behavior")
            return False
            
    except Exception as e:
        print(f"  ✗ Error in test 4: {e}")
        return False
    
    print("\n✓ All tests passed!")
    return True

def test_calculate_woss_function():
    """Test that calculate_woss uses the new helper function"""
    
    print("\nTesting WOSS calculation with new HFC normalization...")
    
    try:
        from utils.processing import calculate_woss
        print("✓ Successfully imported calculate_woss")
    except ImportError as e:
        print(f"✗ Failed to import calculate_woss: {e}")
        return False
    
    # Create test descriptor data
    test_descriptor = {
        'hfc': np.array([0.1, 0.5, 1.0, 2.0, 5.0]),
        'norm_fdom': np.array([0.2, 0.4, 0.6, 0.8, 1.0]),
        'mag_voice_slope': np.array([0.1, 0.2, 0.3, 0.4, 0.5])
    }
    
    plot_settings = {
        'epsilon': 1e-4,
        'fdom_exponent': 2.0
    }
    
    try:
        woss_result = calculate_woss(test_descriptor, plot_settings)
        print(f"  WOSS calculation completed")
        print(f"  Result shape: {woss_result.shape}")
        print(f"  Result range: [{np.min(woss_result):.4f}, {np.max(woss_result):.4f}]")
        
        if woss_result.shape == test_descriptor['hfc'].shape:
            print("  ✓ WOSS result has correct shape")
        else:
            print("  ✗ WOSS result shape mismatch")
            return False
            
        if np.all(np.isfinite(woss_result)):
            print("  ✓ WOSS result contains only finite values")
        else:
            print("  ✗ WOSS result contains non-finite values")
            return False
            
    except Exception as e:
        print(f"  ✗ Error in WOSS calculation: {e}")
        return False
    
    print("✓ WOSS calculation test passed!")
    return True

if __name__ == "__main__":
    print("HFC Normalization Fix Test")
    print("=" * 40)
    
    success = True
    
    # Test the helper function
    if not test_hfc_normalization_function():
        success = False
    
    # Test the WOSS calculation
    if not test_calculate_woss_function():
        success = False
    
    print("\n" + "=" * 40)
    if success:
        print("🎉 All tests passed! HFC normalization fix is working correctly.")
        sys.exit(0)
    else:
        print("❌ Some tests failed. Please check the implementation.")
        sys.exit(1)
