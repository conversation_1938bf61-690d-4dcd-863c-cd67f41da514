#!/usr/bin/env python3
"""
Test script to verify that HFC normalization works correctly in the export functionality.

This script tests:
1. HFC percentile value calculation in Option 1 workflow
2. Robust HFC normalization function in export context
3. Debug information display in export page
4. End-to-end normalization flow from selection to export

Usage:
    python test_export_hfc_fix.py
"""

import numpy as np
import logging
import sys
import os

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_robust_hfc_normalization_in_export_context():
    """Test HFC normalization function in export context scenarios."""
    print("\n" + "="*60)
    print("TESTING HFC NORMALIZATION IN EXPORT CONTEXT")
    print("="*60)
    
    from utils.processing import get_robust_hfc_normalization_value
    
    # Test 1: Simulate session state with HFC percentile value (Option 1 scenario)
    print("\nTest 1: Session state with HFC percentile value (Option 1)")
    
    class MockSessionState:
        def __init__(self):
            self.plot_settings = {
                'hfc_p95': 3.5,
                'hfc_percentile': 95.0
            }
    
    mock_session = MockSessionState()
    hfc_p95, source = get_robust_hfc_normalization_value(session_state=mock_session)
    print(f"  HFC P95: {hfc_p95:.4f} (source: {source})")
    assert hfc_p95 == 3.5, "Should use session state hfc_p95"
    assert "session_state" in source.lower(), "Source should indicate session state"
    
    # Test 2: Simulate plot_settings with HFC percentile value
    print("\nTest 2: Plot settings with HFC percentile value")
    plot_settings = {
        'hfc_p95': 4.2,
        'hfc_percentile': 90.0
    }
    hfc_p95, source = get_robust_hfc_normalization_value(plot_settings=plot_settings)
    print(f"  HFC P90: {hfc_p95:.4f} (source: {source})")
    assert hfc_p95 == 4.2, "Should use plot_settings hfc_p95"
    
    # Test 3: Simulate missing HFC percentile value (fallback scenario)
    print("\nTest 3: Missing HFC percentile value (fallback)")
    empty_plot_settings = {}
    hfc_p95, source = get_robust_hfc_normalization_value(plot_settings=empty_plot_settings)
    print(f"  HFC P95: {hfc_p95:.4f} (source: {source})")
    assert hfc_p95 == 1.0, "Should fallback to 1.0"
    assert "fallback" in source.lower(), "Source should indicate fallback"
    
    print("✅ All HFC normalization in export context tests passed!")

def test_debug_information_format():
    """Test the debug information format that would be displayed in export page."""
    print("\n" + "="*60)
    print("TESTING DEBUG INFORMATION FORMAT")
    print("="*60)
    
    from utils.processing import get_robust_hfc_normalization_value, get_robust_spec_decrease_normalization_value
    
    # Test 1: Debug format with valid values
    print("\nTest 1: Debug format with valid values")
    plot_settings = {
        'hfc_p95': 3.8,
        'hfc_percentile': 95.0,
        'spec_decrease_p95': 2.1,
        'spec_decrease_percentile': 95.0
    }
    
    # Simulate the debug display logic from export_results_page.py
    try:
        hfc_p95_debug, hfc_source_debug = get_robust_hfc_normalization_value(plot_settings=plot_settings)
        hfc_percentile = plot_settings.get('hfc_percentile', 95.0)
        debug_message_hfc = f"HFC p{int(hfc_percentile)}: {hfc_p95_debug:.4f} (source: {hfc_source_debug})"
        print(f"  {debug_message_hfc}")
        
        spec_decrease_p95_debug, spec_decrease_source_debug = get_robust_spec_decrease_normalization_value(plot_settings=plot_settings)
        spec_decrease_percentile = plot_settings.get('spec_decrease_percentile', 95.0)
        debug_message_spec = f"Spectral Decrease p{int(spec_decrease_percentile)}: {spec_decrease_p95_debug:.4f} (source: {spec_decrease_source_debug})"
        print(f"  {debug_message_spec}")
        
        assert "HFC p95: 3.8000" in debug_message_hfc, "HFC debug message should show correct value"
        assert "Spectral Decrease p95: 2.1000" in debug_message_spec, "Spectral Decrease debug message should show correct value"
        
    except Exception as e:
        print(f"  Error in debug format test: {e}")
        assert False, f"Debug format test should not raise exception: {e}"
    
    # Test 2: Debug format with missing values
    print("\nTest 2: Debug format with missing values")
    empty_plot_settings = {}
    
    try:
        hfc_p95_debug, hfc_source_debug = get_robust_hfc_normalization_value(plot_settings=empty_plot_settings)
        hfc_percentile = empty_plot_settings.get('hfc_percentile', 95.0)
        debug_message_hfc = f"HFC p{int(hfc_percentile)}: {hfc_p95_debug:.4f} (source: {hfc_source_debug})"
        print(f"  {debug_message_hfc}")
        
        assert "HFC p95: 1.0000" in debug_message_hfc, "HFC debug message should show fallback value"
        assert "fallback" in hfc_source_debug.lower(), "Source should indicate fallback"
        
    except Exception as e:
        print(f"  Error in debug format test with missing values: {e}")
        assert False, f"Debug format test should not raise exception: {e}"
    
    print("✅ All debug information format tests passed!")

def test_option1_workflow_simulation():
    """Test simulation of Option 1 workflow percentile calculation."""
    print("\n" + "="*60)
    print("TESTING OPTION 1 WORKFLOW SIMULATION")
    print("="*60)
    
    # Test 1: Simulate percentile calculation from trace data
    print("\nTest 1: Percentile calculation from simulated trace data")
    
    # Create realistic HFC and Spectral Decrease data
    num_samples = 1000
    hfc_data = np.random.exponential(scale=2.0, size=num_samples)
    spec_decrease_data = np.random.exponential(scale=1.5, size=num_samples)
    
    # Simulate the percentile calculation that would happen in Option 1
    hfc_percentile = 95.0
    spec_decrease_percentile = 95.0
    
    hfc_p95 = np.percentile(hfc_data, hfc_percentile)
    spec_decrease_p95 = np.percentile(spec_decrease_data, spec_decrease_percentile)
    
    print(f"  Calculated HFC p{hfc_percentile}: {hfc_p95:.4f}")
    print(f"  Calculated Spectral Decrease p{spec_decrease_percentile}: {spec_decrease_p95:.4f}")
    
    # Verify the values are reasonable
    assert hfc_p95 > 0, "HFC percentile should be positive"
    assert spec_decrease_p95 > 0, "Spectral Decrease percentile should be positive"
    assert hfc_p95 > np.mean(hfc_data), "HFC p95 should be greater than mean"
    assert spec_decrease_p95 > np.mean(spec_decrease_data), "Spectral Decrease p95 should be greater than mean"
    
    # Test 2: Simulate session state storage and retrieval
    print("\nTest 2: Session state storage and retrieval simulation")
    
    # Simulate storing in session state (as would happen in Option 1)
    simulated_session_state = {
        'plot_settings': {
            'hfc_p95': float(hfc_p95),
            'spec_decrease_p95': float(spec_decrease_p95),
            'hfc_percentile': hfc_percentile,
            'spec_decrease_percentile': spec_decrease_percentile
        }
    }
    
    # Simulate retrieval in export (using robust helper functions)
    from utils.processing import get_robust_hfc_normalization_value, get_robust_spec_decrease_normalization_value
    
    retrieved_hfc_p95, hfc_source = get_robust_hfc_normalization_value(
        plot_settings=simulated_session_state['plot_settings']
    )
    retrieved_spec_decrease_p95, spec_decrease_source = get_robust_spec_decrease_normalization_value(
        plot_settings=simulated_session_state['plot_settings']
    )
    
    print(f"  Retrieved HFC p{hfc_percentile}: {retrieved_hfc_p95:.4f} (source: {hfc_source})")
    print(f"  Retrieved Spectral Decrease p{spec_decrease_percentile}: {retrieved_spec_decrease_p95:.4f} (source: {spec_decrease_source})")
    
    # Verify the values match
    assert abs(retrieved_hfc_p95 - hfc_p95) < 1e-6, "Retrieved HFC value should match stored value"
    assert abs(retrieved_spec_decrease_p95 - spec_decrease_p95) < 1e-6, "Retrieved Spectral Decrease value should match stored value"
    
    print("✅ All Option 1 workflow simulation tests passed!")

def main():
    """Run all tests."""
    print("Starting HFC export fix verification tests...")
    
    try:
        test_robust_hfc_normalization_in_export_context()
        test_debug_information_format()
        test_option1_workflow_simulation()
        
        print("\n" + "="*60)
        print("🎉 ALL EXPORT HFC FIX TESTS PASSED! 🎉")
        print("="*60)
        print("\nHFC normalization in export functionality is working correctly!")
        print("✅ Robust HFC normalization in export context: PASS")
        print("✅ Debug information format: PASS")
        print("✅ Option 1 workflow simulation: PASS")
        print("\nThe fix should resolve the 'HFC p95: Not set' issue in export functionality.")
        
    except Exception as e:
        print(f"\n❌ TEST FAILED: {e}")
        logging.error(f"Test failed: {e}", exc_info=True)
        sys.exit(1)

if __name__ == "__main__":
    main()
